# PDF 预览功能使用说明

本项目使用 PDF.js 实现了完整的 PDF 预览功能，包含以下特性：

## 功能特性

1. **PDF 文档渲染**：使用 PDF.js 在 Canvas 上渲染 PDF 页面
2. **页面导航**：支持上一页/下一页翻页
3. **缩放控制**：支持放大/缩小功能
4. **下载功能**：支持下载原始 PDF 文件
5. **响应式设计**：适配移动端和桌面端
6. **错误处理**：优雅处理加载失败的情况
7. **模态框显示**：以弹窗形式展示 PDF 内容

## 组件结构

### PdfViewer.vue
核心 PDF 预览组件，负责：
- PDF 文档加载和渲染
- 页面导航控制
- 缩放功能
- Canvas 渲染管理

### PdfModal.vue
PDF 预览模态框组件，负责：
- 模态框的显示和隐藏
- 标题和操作按钮
- 键盘事件处理（ESC 关闭）

## 使用方法

### 1. 在组件中引入

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <button @click="openPdfPreview">查看PDF</button>
    
    <!-- PDF预览模态框 -->
    <PdfModal 
      :visible="pdfModalVisible"
      :pdf-url="pdfUrl"
      :title="pdfTitle"
      @close="closePdfModal"
    />
  </div>
</template>

<script>
import PdfModal from '@/components/PdfModal.vue';

export default {
  components: {
    PdfModal
  },
  data() {
    return {
      pdfModalVisible: false,
      pdfUrl: '',
      pdfTitle: ''
    };
  },
  methods: {
    openPdfPreview() {
      this.pdfUrl = 'https://example.com/document.pdf';
      this.pdfTitle = '文档预览';
      this.pdfModalVisible = true;
    },
    
    closePdfModal() {
      this.pdfModalVisible = false;
      this.pdfUrl = '';
      this.pdfTitle = '';
    }
  }
};
</script>
```

### 2. 直接使用 PdfViewer 组件

```vue
<template>
  <div style="width: 800px; height: 600px;">
    <PdfViewer 
      :pdf-url="pdfUrl"
      :title="title"
    />
  </div>
</template>

<script>
import PdfViewer from '@/components/PdfViewer.vue';

export default {
  components: {
    PdfViewer
  },
  data() {
    return {
      pdfUrl: 'https://example.com/document.pdf',
      title: '文档预览'
    };
  }
};
</script>
```

## 配置说明

### PDF.js Worker 配置

组件会自动配置 PDF.js Worker，使用 CDN 地址：
```javascript
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
```

如果需要使用本地 Worker 文件，可以：

1. 将 `pdf.worker.min.js` 文件放到 `public` 目录
2. 修改 Worker 路径：
```javascript
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';
```

### 支持的 PDF 源

- **HTTP/HTTPS URL**：公开可访问的 PDF 文件
- **本地文件**：通过 File API 上传的文件
- **Base64 数据**：data:application/pdf;base64,... 格式
- **ArrayBuffer**：二进制数据

## 注意事项

1. **跨域问题**：确保 PDF 文件服务器支持 CORS
2. **文件大小**：大文件可能加载较慢，建议添加加载进度提示
3. **浏览器兼容性**：现代浏览器都支持，IE 需要 polyfill
4. **内存管理**：组件会自动清理资源，避免内存泄漏

## 自定义样式

可以通过 CSS 变量或覆盖样式来自定义外观：

```css
/* 自定义工具栏颜色 */
.pdf-toolbar {
  background: #your-color !important;
}

/* 自定义按钮样式 */
.pdf-btn {
  color: #your-color !important;
}
```

## 错误处理

组件内置了错误处理机制：
- 网络错误：显示错误信息和下载按钮
- 文件格式错误：提示用户文件不是有效的 PDF
- 渲染错误：自动重试或显示错误信息

## 性能优化

1. **懒加载**：只有在需要时才加载 PDF.js
2. **页面缓存**：已渲染的页面会被缓存
3. **取消渲染**：切换页面时会取消正在进行的渲染任务
4. **资源清理**：组件销毁时自动清理所有资源
