<template>
  <div class="pdf-viewer">
    <div class="pdf-toolbar">
      <div class="pdf-info">
        <span v-if="pageNum">{{ pageNum }} / {{ pageCount }}</span>
        <span v-else>加载中...</span>
      </div>
      <div class="pdf-controls">
        <button @click="prevPage" :disabled="pageNum <= 1 || loading" class="pdf-btn">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button @click="nextPage" :disabled="pageNum >= pageCount || loading" class="pdf-btn">
          <i class="fas fa-chevron-right"></i>
        </button>
        <button @click="zoomIn" :disabled="loading" class="pdf-btn">
          <i class="fas fa-search-plus"></i>
        </button>
        <button @click="zoomOut" :disabled="loading" class="pdf-btn">
          <i class="fas fa-search-minus"></i>
        </button>
        <button @click="downloadPdf" :disabled="loading" class="pdf-btn">
          <i class="fas fa-download"></i>
        </button>
      </div>
    </div>
    
    <div class="pdf-container" ref="pdfContainer">
      <div v-if="loading" class="pdf-loading">
        <i class="fas fa-spinner fa-spin"></i>
        <span>PDF 加载中...</span>
      </div>
      <div v-if="error" class="pdf-error">
        <i class="fas fa-exclamation-triangle"></i>
        <h4>加载失败</h4>
        <p>{{ error }}</p>
        <button @click="downloadPdf" class="pdf-download-btn">
          <i class="fas fa-download"></i> 下载PDF
        </button>
      </div>
      <canvas ref="pdfCanvas" class="pdf-canvas"></canvas>
    </div>
  </div>
</template>

<script>
import * as pdfjsLib from 'pdfjs-dist';
import { onMounted, onBeforeUnmount, ref, watch } from 'vue';

// 设置 PDF.js worker 路径 - 使用动态版本匹配
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

export default {
  name: 'PdfViewer',
  props: {
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: '文档预览'
    }
  },
  setup(props) {
    const pdfContainer = ref(null);
    const pdfCanvas = ref(null);
    const loading = ref(true);
    const error = ref(null);
    const pageNum = ref(1);
    const pageCount = ref(0);
    const scale = ref(1.0);
    
    let pdfDoc = null;
    let pdfTask = null;
    let currentRenderTask = null;

    // 加载PDF文档
    const loadPdf = async () => {
      if (pdfTask) {
        try {
          await pdfTask.destroy();
        } catch (e) {
          // 忽略销毁错误
        }
        pdfTask = null;
      }

      loading.value = true;
      error.value = null;

      try {
        // 为 2.5.207 版本配置加载选项
        const loadingTask = pdfjsLib.getDocument({
          url: props.pdfUrl,
          cMapUrl: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.5.207/cmaps/',
          cMapPacked: true,
          // 禁用字体加载以提高兼容性
          disableFontFace: false,
          // 设置最大图像大小
          maxImageSize: 1024 * 1024
        });

        pdfTask = loadingTask;
        pdfDoc = await loadingTask.promise;
        pageCount.value = pdfDoc.numPages;

        // 渲染第一页
        pageNum.value = 1;
        await renderPage(1);

        loading.value = false;
      } catch (err) {
        console.error('PDF加载失败:', err);
        error.value = err.message || '无法加载PDF文档';
        loading.value = false;
      }
    };

    // 渲染指定页面
    const renderPage = async (num) => {
      if (!pdfDoc) return;

      try {
        // 如果有正在进行的渲染任务，取消它
        if (currentRenderTask) {
          try {
            currentRenderTask.cancel();
          } catch (e) {
            // 忽略取消错误
          }
          currentRenderTask = null;
        }

        // 获取页面
        const page = await pdfDoc.getPage(num);

        // 设置缩放比例和视口 - 兼容新版本的 API
        const viewport = page.getViewport({ scale: scale.value });

        // 设置canvas尺寸
        const canvas = pdfCanvas.value;
        if (!canvas) return;

        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        // 清除之前的内容
        context.clearRect(0, 0, canvas.width, canvas.height);

        // 渲染PDF页面到Canvas
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };

        currentRenderTask = page.render(renderContext);
        await currentRenderTask.promise;
        currentRenderTask = null;

        pageNum.value = num;
      } catch (err) {
        if (err && err.name !== 'RenderingCancelledException') {
          console.error('页面渲染失败:', err);
          error.value = `渲染页面 ${num} 失败: ${err.message}`;
        }
      }
    };

    // 上一页
    const prevPage = () => {
      if (pageNum.value <= 1) return;
      renderPage(pageNum.value - 1);
    };

    // 下一页
    const nextPage = () => {
      if (pageNum.value >= pageCount.value) return;
      renderPage(pageNum.value + 1);
    };

    // 放大
    const zoomIn = () => {
      scale.value = Math.min(scale.value + 0.2, 3.0);
      renderPage(pageNum.value);
    };

    // 缩小
    const zoomOut = () => {
      scale.value = Math.max(scale.value - 0.2, 0.5);
      renderPage(pageNum.value);
    };

    // 下载PDF
    const downloadPdf = () => {
      window.open(props.pdfUrl, '_blank');
    };

    // 监听URL变化，重新加载PDF
    watch(() => props.pdfUrl, (newUrl, oldUrl) => {
      if (newUrl !== oldUrl) {
        loadPdf();
      }
    });

    // 组件挂载时加载PDF
    onMounted(() => {
      loadPdf();
    });

    // 组件卸载前清理资源
    onBeforeUnmount(async () => {
      // 清理渲染任务
      if (currentRenderTask) {
        try {
          currentRenderTask.cancel();
        } catch (e) {
          // 忽略取消错误
        }
        currentRenderTask = null;
      }

      // 清理PDF文档
      if (pdfDoc) {
        try {
          pdfDoc.destroy();
        } catch (e) {
          // 忽略销毁错误
        }
        pdfDoc = null;
      }

      // 清理加载任务
      if (pdfTask) {
        try {
          await pdfTask.destroy();
        } catch (e) {
          // 忽略销毁错误
        }
        pdfTask = null;
      }
    });

    return {
      pdfContainer,
      pdfCanvas,
      loading,
      error,
      pageNum,
      pageCount,
      prevPage,
      nextPage,
      zoomIn,
      zoomOut,
      downloadPdf
    };
  }
}
</script>

<style scoped>
.pdf-viewer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.pdf-info {
  font-size: 14px;
  color: #555;
}

.pdf-controls {
  display: flex;
  gap: 8px;
}

.pdf-btn {
  background: none;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  color: #4682B4;
  transition: all 0.2s;
}

.pdf-btn:hover:not(:disabled) {
  background: #f0f0f0;
  color: #1E90FF;
}

.pdf-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.pdf-container {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  position: relative;
}

.pdf-canvas {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background: white;
}

.pdf-loading, .pdf-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.pdf-loading i, .pdf-error i {
  font-size: 32px;
  margin-bottom: 15px;
  display: block;
}

.pdf-error i {
  color: #dc3545;
}

.pdf-error h4 {
  margin: 10px 0;
  color: #333;
}

.pdf-download-btn {
  background: #4682B4;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  margin-top: 15px;
  cursor: pointer;
  transition: background 0.2s;
}

.pdf-download-btn:hover {
  background: #1E90FF;
}
</style>
