<template>
  <div class="pdf-debug">
    <h3>PDF 调试组件</h3>
    <div class="debug-info">
      <p>PDF URL: {{ pdfUrl }}</p>
      <p>加载状态: {{ loading ? '加载中' : '已加载' }}</p>
      <p>错误信息: {{ error || '无' }}</p>
      <p>页数: {{ pageCount }}</p>
      <p>当前页: {{ pageNum }}</p>
      <p>缩放: {{ scale }}</p>
    </div>
    
    <div class="debug-controls">
      <button @click="testRender" :disabled="loading">测试渲染</button>
      <button @click="prevPage" :disabled="pageNum <= 1">上一页</button>
      <button @click="nextPage" :disabled="pageNum >= pageCount">下一页</button>
    </div>
    
    <div class="canvas-container">
      <canvas 
        ref="debugCanvas" 
        style="border: 2px solid red; background: white; max-width: 100%;"
      ></canvas>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfDebug',
  props: {
    pdfUrl: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      pageNum: 1,
      pageCount: 0,
      scale: 1.0,
      pdfDoc: null,
      pdfjsLib: null
    };
  },
  async mounted() {
    await this.initPdf();
  },
  methods: {
    async initPdf() {
      try {
        console.log('开始初始化 PDF.js...');
        this.pdfjsLib = await import('pdfjs-dist');
        
        // 设置 Worker
        const version = this.pdfjsLib.version;
        this.pdfjsLib.GlobalWorkerOptions.workerSrc = 
          `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${version}/pdf.worker.min.js`;
        
        console.log('PDF.js 版本:', version);
        console.log('Worker 路径:', this.pdfjsLib.GlobalWorkerOptions.workerSrc);
        
        await this.loadPdf();
      } catch (err) {
        console.error('初始化失败:', err);
        this.error = err.message;
        this.loading = false;
      }
    },
    
    async loadPdf() {
      try {
        console.log('开始加载 PDF:', this.pdfUrl);
        this.loading = true;
        this.error = null;
        
        const loadingTask = this.pdfjsLib.getDocument({
          url: this.pdfUrl,
          disableFontFace: false,
          disableRange: false,
          disableStream: false
        });
        
        this.pdfDoc = await loadingTask.promise;
        this.pageCount = this.pdfDoc.numPages;
        
        console.log('PDF 加载成功，页数:', this.pageCount);
        
        this.loading = false;
        await this.renderPage(1);
      } catch (err) {
        console.error('PDF 加载失败:', err);
        this.error = err.message;
        this.loading = false;
      }
    },
    
    async renderPage(num) {
      if (!this.pdfDoc) {
        console.log('PDF 文档未加载');
        return;
      }
      
      try {
        console.log('开始渲染页面:', num);
        
        const page = await this.pdfDoc.getPage(num);
        console.log('获取页面成功');
        
        // 获取视口
        const viewport = page.getViewport({ scale: this.scale });
        console.log('视口尺寸:', viewport.width, 'x', viewport.height);
        
        // 设置 Canvas
        const canvas = this.$refs.debugCanvas;
        if (!canvas) {
          console.error('Canvas 元素未找到');
          return;
        }
        
        const context = canvas.getContext('2d');
        canvas.width = viewport.width;
        canvas.height = viewport.height;
        
        console.log('Canvas 尺寸设置为:', canvas.width, 'x', canvas.height);
        
        // 清除画布
        context.clearRect(0, 0, canvas.width, canvas.height);
        
        // 填充白色背景用于测试
        context.fillStyle = 'white';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        // 渲染 PDF
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };
        
        console.log('开始渲染...');
        const renderTask = page.render(renderContext);
        await renderTask.promise;
        
        console.log('渲染完成');
        this.pageNum = num;
        
      } catch (err) {
        console.error('渲染失败:', err);
        this.error = `渲染失败: ${err.message}`;
      }
    },
    
    async testRender() {
      console.log('手动测试渲染');
      await this.renderPage(this.pageNum);
    },
    
    async prevPage() {
      if (this.pageNum > 1) {
        await this.renderPage(this.pageNum - 1);
      }
    },
    
    async nextPage() {
      if (this.pageNum < this.pageCount) {
        await this.renderPage(this.pageNum + 1);
      }
    }
  }
};
</script>

<style scoped>
.pdf-debug {
  padding: 20px;
  border: 2px solid #ccc;
  margin: 20px;
}

.debug-info {
  background: #f5f5f5;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

.debug-info p {
  margin: 5px 0;
  font-family: monospace;
}

.debug-controls {
  margin-bottom: 20px;
}

.debug-controls button {
  margin-right: 10px;
  padding: 5px 10px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.debug-controls button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.canvas-container {
  border: 1px solid #ddd;
  padding: 10px;
  background: #f9f9f9;
  text-align: center;
}
</style>
