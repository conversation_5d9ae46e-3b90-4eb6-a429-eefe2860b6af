<template>
  <div class="pdf-test-page">
    <div class="test-container">
      <h1>PDF 预览功能测试</h1>
      
      <div class="test-section">
        <h2>测试用例</h2>
        <div class="test-buttons">
          <button 
            v-for="testCase in testCases" 
            :key="testCase.id"
            @click="openPdf(testCase)"
            class="test-btn"
          >
            <i :class="testCase.icon"></i>
            {{ testCase.name }}
          </button>
        </div>
      </div>

      <div class="test-section">
        <h2>文件上传测试</h2>
        <div class="upload-area">
          <input 
            type="file" 
            accept=".pdf"
            @change="handleFileUpload"
            ref="fileInput"
            style="display: none;"
          >
          <button @click="$refs.fileInput.click()" class="upload-btn">
            <i class="fas fa-upload"></i>
            选择本地PDF文件
          </button>
          <p class="upload-hint">支持上传 PDF 文件进行预览测试</p>
        </div>
      </div>

      <div class="test-section">
        <h2>自定义URL测试</h2>
        <div class="url-input-area">
          <input 
            v-model="customUrl"
            type="url"
            placeholder="输入PDF文件URL"
            class="url-input"
          >
          <button 
            @click="openCustomPdf"
            :disabled="!customUrl"
            class="test-btn"
          >
            <i class="fas fa-external-link-alt"></i>
            预览
          </button>
        </div>
      </div>
    </div>

    <!-- PDF预览模态框 -->
    <PdfModal 
      :visible="pdfModalVisible"
      :pdf-url="currentPdfUrl"
      :title="currentPdfTitle"
      @close="closePdfModal"
    />
  </div>
</template>

<script>
import PdfModal from '@/components/PdfModal.vue';

export default {
  name: 'PdfTest',
  components: {
    PdfModal
  },
  data() {
    return {
      pdfModalVisible: false,
      currentPdfUrl: '',
      currentPdfTitle: '',
      customUrl: '',
      testCases: [
        {
          id: 1,
          name: 'Mozilla PDF.js 示例',
          url: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
          icon: 'fas fa-file-pdf'
        },
        {
          id: 2,
          name: 'W3C 测试文档',
          url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
          icon: 'fas fa-file-alt'
        },
        {
          id: 3,
          name: 'PDF.js 官方示例',
          url: 'https://raw.githubusercontent.com/mozilla/pdf.js/ba2edeae/examples/learning/helloworld.pdf',
          icon: 'fas fa-graduation-cap'
        }
      ]
    };
  },
  methods: {
    openPdf(testCase) {
      this.currentPdfUrl = testCase.url;
      this.currentPdfTitle = testCase.name;
      this.pdfModalVisible = true;
    },

    openCustomPdf() {
      if (!this.customUrl) return;
      
      this.currentPdfUrl = this.customUrl;
      this.currentPdfTitle = '自定义PDF文档';
      this.pdfModalVisible = true;
    },

    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      if (file.type !== 'application/pdf') {
        alert('请选择PDF文件');
        return;
      }

      // 创建文件URL
      const fileUrl = URL.createObjectURL(file);
      this.currentPdfUrl = fileUrl;
      this.currentPdfTitle = `本地文件: ${file.name}`;
      this.pdfModalVisible = true;

      // 清空文件输入
      event.target.value = '';
    },

    closePdfModal() {
      // 如果是本地文件URL，需要释放
      if (this.currentPdfUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.currentPdfUrl);
      }
      
      this.pdfModalVisible = false;
      this.currentPdfUrl = '';
      this.currentPdfTitle = '';
    }
  }
};
</script>

<style scoped>
.pdf-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.test-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 28px;
}

.test-section {
  margin-bottom: 40px;
}

.test-section h2 {
  color: #555;
  margin-bottom: 20px;
  font-size: 20px;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.test-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.test-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 15px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.test-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.test-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.upload-area {
  text-align: center;
  padding: 30px;
  border: 2px dashed #667eea;
  border-radius: 8px;
  background: #f8f9ff;
}

.upload-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: background 0.3s ease;
}

.upload-btn:hover {
  background: #218838;
}

.upload-hint {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.url-input-area {
  display: flex;
  gap: 10px;
  align-items: center;
}

.url-input {
  flex: 1;
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.url-input:focus {
  outline: none;
  border-color: #667eea;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-container {
    padding: 20px;
    margin: 10px;
  }
  
  .test-buttons {
    grid-template-columns: 1fr;
  }
  
  .url-input-area {
    flex-direction: column;
  }
  
  .url-input {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>
